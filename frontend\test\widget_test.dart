// Hospital Management System Widget Tests
//
// This file contains widget tests for the Hospital Management System.

import 'package:flutter_test/flutter_test.dart';
import 'package:hospital_management_frontend/main.dart';

void main() {
  group('Hospital Management System Tests', () {
    testWidgets('App loads and shows login screen', (WidgetTester tester) async {
      // Build our app and trigger a frame.
      await tester.pumpWidget(const HospitalManagementApp());
      await tester.pumpAndSettle();

      // Verify that login screen elements are present
      expect(find.text('Hospital Management'), findsOneWidget);
      expect(find.text('Welcome Back'), findsOneWidget);
      expect(find.text('Username'), findsOneWidget);
      expect(find.text('Password'), findsOneWidget);
      expect(find.text('Sign In'), findsOneWidget);
    });

    testWidgets('Login form validation works', (WidgetTester tester) async {
      await tester.pumpWidget(const HospitalManagementApp());
      await tester.pumpAndSettle();

      // Try to submit empty form
      await tester.tap(find.text('Sign In'));
      await tester.pumpAndSettle();

      // Should show validation errors
      expect(find.text('Please enter your username'), findsOneWidget);
      expect(find.text('Please enter your password'), findsOneWidget);
    });

    testWidgets('Demo credentials are displayed', (WidgetTester tester) async {
      await tester.pumpWidget(const HospitalManagementApp());
      await tester.pumpAndSettle();

      // Check if demo credentials section is visible
      expect(find.text('Demo Credentials:'), findsOneWidget);
      expect(find.textContaining('admin / admin123'), findsOneWidget);
    });
  });
}
