import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'services/api_service.dart';
import 'providers/patient_provider.dart';
import 'providers/doctor_provider.dart';
import 'providers/appointment_provider.dart';
import 'providers/billing_provider.dart';
import 'providers/analytics_provider.dart';
import 'screens/dashboard_screen.dart';
import 'screens/patients/patient_list_screen.dart';
import 'screens/patients/patient_detail_screen.dart';
import 'screens/patients/add_patient_screen.dart';
import 'screens/doctors/doctor_list_screen.dart';
import 'screens/doctors/doctor_detail_screen.dart';
import 'screens/appointments/appointment_list_screen.dart';
import 'screens/appointments/book_appointment_screen.dart';
import 'screens/billing/billing_list_screen.dart';
import 'screens/analytics/analytics_screen.dart';

void main() {
  runApp(const HospitalManagementApp());
}

class HospitalManagementApp extends StatelessWidget {
  const HospitalManagementApp({super.key});

  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
      designSize: const Size(375, 812),
      minTextAdapt: true,
      splitScreenMode: true,
      builder: (context, child) {
        return MultiProvider(
          providers: [
            Provider<ApiService>(
              create: (_) => ApiService(),
            ),
            ChangeNotifierProxyProvider<ApiService, PatientProvider>(
              create: (context) => PatientProvider(
                Provider.of<ApiService>(context, listen: false),
              ),
              update: (context, apiService, previous) =>
                  previous ?? PatientProvider(apiService),
            ),
            ChangeNotifierProxyProvider<ApiService, DoctorProvider>(
              create: (context) => DoctorProvider(
                Provider.of<ApiService>(context, listen: false),
              ),
              update: (context, apiService, previous) =>
                  previous ?? DoctorProvider(apiService),
            ),
            ChangeNotifierProxyProvider<ApiService, AppointmentProvider>(
              create: (context) => AppointmentProvider(
                Provider.of<ApiService>(context, listen: false),
              ),
              update: (context, apiService, previous) =>
                  previous ?? AppointmentProvider(apiService),
            ),
            ChangeNotifierProxyProvider<ApiService, BillingProvider>(
              create: (context) => BillingProvider(
                Provider.of<ApiService>(context, listen: false),
              ),
              update: (context, apiService, previous) =>
                  previous ?? BillingProvider(apiService),
            ),
            ChangeNotifierProxyProvider<ApiService, AnalyticsProvider>(
              create: (context) => AnalyticsProvider(
                Provider.of<ApiService>(context, listen: false),
              ),
              update: (context, apiService, previous) =>
                  previous ?? AnalyticsProvider(apiService),
            ),
          ],
          child: MaterialApp.router(
            title: 'Hospital Management System',
            debugShowCheckedModeBanner: false,
            theme: ThemeData(
              primarySwatch: Colors.blue,
              primaryColor: const Color(0xFF2196F3),
              colorScheme: ColorScheme.fromSeed(
                seedColor: const Color(0xFF2196F3),
                brightness: Brightness.light,
              ),
              useMaterial3: true,
              appBarTheme: const AppBarTheme(
                backgroundColor: Color(0xFF2196F3),
                foregroundColor: Colors.white,
                elevation: 2,
              ),
              cardTheme: CardTheme(
                elevation: 4,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              elevatedButtonTheme: ElevatedButtonThemeData(
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF2196F3),
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ),
            routerConfig: _router,
          ),
        );
      },
    );
  }
}

final GoRouter _router = GoRouter(
  initialLocation: '/',
  routes: [
    GoRoute(
      path: '/',
      name: 'dashboard',
      builder: (context, state) => const DashboardScreen(),
    ),
    GoRoute(
      path: '/patients',
      name: 'patients',
      builder: (context, state) => const PatientListScreen(),
      routes: [
        GoRoute(
          path: '/add',
          name: 'add-patient',
          builder: (context, state) => const AddPatientScreen(),
        ),
        GoRoute(
          path: '/:id',
          name: 'patient-detail',
          builder: (context, state) {
            final id = int.parse(state.pathParameters['id']!);
            return PatientDetailScreen(patientId: id);
          },
        ),
      ],
    ),
    GoRoute(
      path: '/doctors',
      name: 'doctors',
      builder: (context, state) => const DoctorListScreen(),
      routes: [
        GoRoute(
          path: '/:id',
          name: 'doctor-detail',
          builder: (context, state) {
            final id = int.parse(state.pathParameters['id']!);
            return DoctorDetailScreen(doctorId: id);
          },
        ),
      ],
    ),
    GoRoute(
      path: '/appointments',
      name: 'appointments',
      builder: (context, state) => const AppointmentListScreen(),
      routes: [
        GoRoute(
          path: '/book',
          name: 'book-appointment',
          builder: (context, state) => const BookAppointmentScreen(),
        ),
      ],
    ),
    GoRoute(
      path: '/billing',
      name: 'billing',
      builder: (context, state) => const BillingListScreen(),
    ),
    GoRoute(
      path: '/analytics',
      name: 'analytics',
      builder: (context, state) => const AnalyticsScreen(),
    ),
  ],
);
