import 'package:flutter/foundation.dart';
import '../models/doctor.dart';
import '../services/api_service.dart';

class <PERSON><PERSON><PERSON><PERSON> extends ChangeNotifier {
  final ApiService _apiService;

  DoctorProvider(this._apiService);

  List<Doctor> _doctors = [];
  Doctor? _selectedDoctor;
  bool _isLoading = false;
  String? _error;
  int _currentPage = 1;
  bool _hasMoreData = true;

  // Getters
  List<Doctor> get doctors => _doctors;
  Doctor? get selectedDoctor => _selectedDoctor;
  bool get isLoading => _isLoading;
  String? get error => _error;
  int get currentPage => _currentPage;
  bool get hasMoreData => _hasMoreData;

  // Load doctors with pagination and filters
  Future<void> loadDoctors({
    bool refresh = false,
    String? status,
    String? specialization,
    int? departmentId,
    String? search,
  }) async {
    if (refresh) {
      _currentPage = 1;
      _doctors.clear();
      _hasMoreData = true;
    }

    if (_isLoading || !_hasMoreData) return;

    _setLoading(true);
    _setError(null);

    try {
      final response = await _apiService.getDoctors(
        page: _currentPage,
        limit: 20,
        status: status,
        specialization: specialization,
        departmentId: departmentId,
        search: search,
      );

      if (response['success'] == true) {
        final List<dynamic> doctorData = response['data'] as List<dynamic>;
        final List<Doctor> newDoctors = doctorData
            .map((json) => Doctor.fromJson(json as Map<String, dynamic>))
            .toList();

        if (refresh) {
          _doctors = newDoctors;
        } else {
          _doctors.addAll(newDoctors);
        }

        _currentPage++;
        _hasMoreData = newDoctors.length == 20;
      } else {
        _setError(response['error'] ?? 'Failed to load doctors');
      }
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  // Load a specific doctor
  Future<void> loadDoctor(int id) async {
    _setLoading(true);
    _setError(null);

    try {
      final response = await _apiService.getDoctor(id);

      if (response['success'] == true) {
        _selectedDoctor = Doctor.fromJson(response['data'] as Map<String, dynamic>);
      } else {
        _setError(response['error'] ?? 'Failed to load doctor');
      }
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  // Get available doctors for a specific date and time
  Future<List<Doctor>> getAvailableDoctors(String date, String time) async {
    _setLoading(true);
    _setError(null);

    try {
      final response = await _apiService.getAvailableDoctors(date, time);

      if (response['success'] == true) {
        final List<dynamic> doctorData = response['data'] as List<dynamic>;
        return doctorData
            .map((json) => Doctor.fromJson(json as Map<String, dynamic>))
            .toList();
      } else {
        _setError(response['error'] ?? 'Failed to load available doctors');
        return [];
      }
    } catch (e) {
      _setError(e.toString());
      return [];
    } finally {
      _setLoading(false);
    }
  }

  // Search doctors
  Future<void> searchDoctors(String query) async {
    await loadDoctors(refresh: true, search: query);
  }

  // Filter doctors by specialization
  Future<void> filterBySpecialization(String specialization) async {
    await loadDoctors(refresh: true, specialization: specialization);
  }

  // Filter doctors by status
  Future<void> filterByStatus(String status) async {
    await loadDoctors(refresh: true, status: status);
  }

  // Filter doctors by department
  Future<void> filterByDepartment(int departmentId) async {
    await loadDoctors(refresh: true, departmentId: departmentId);
  }

  // Clear selected doctor
  void clearSelectedDoctor() {
    _selectedDoctor = null;
    notifyListeners();
  }

  // Clear error
  void clearError() {
    _setError(null);
  }

  // Private helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }

  // Refresh data
  Future<void> refresh() async {
    await loadDoctors(refresh: true);
  }
}
