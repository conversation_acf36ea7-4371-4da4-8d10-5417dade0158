import 'package:flutter/foundation.dart';
import '../services/api_service.dart';

class AnalyticsProvider extends ChangeNotifier {
  final ApiService _apiService;

  AnalyticsProvider(this._apiService);

  Map<String, dynamic>? _dashboardData;
  Map<String, dynamic>? _patientDemographics;
  Map<String, dynamic>? _doctorPerformance;
  Map<String, dynamic>? _financialSummary;
  Map<String, dynamic>? _appointmentTrends;
  bool _isLoading = false;
  String? _error;

  // Getters
  Map<String, dynamic>? get dashboardData => _dashboardData;
  Map<String, dynamic>? get patientDemographics => _patientDemographics;
  Map<String, dynamic>? get doctorPerformance => _doctorPerformance;
  Map<String, dynamic>? get financialSummary => _financialSummary;
  Map<String, dynamic>? get appointmentTrends => _appointmentTrends;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // Load dashboard analytics
  Future<void> loadDashboardAnalytics() async {
    _setLoading(true);
    _setError(null);

    try {
      final response = await _apiService.getDashboardAnalytics();

      if (response['success'] == true) {
        _dashboardData = response['data'] as Map<String, dynamic>;
      } else {
        _setError(response['error'] ?? 'Failed to load dashboard analytics');
      }
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  // Load patient demographics
  Future<void> loadPatientDemographics() async {
    _setLoading(true);
    _setError(null);

    try {
      final response = await _apiService.getPatientDemographics();

      if (response['success'] == true) {
        _patientDemographics = response['data'] as Map<String, dynamic>;
      } else {
        _setError(response['error'] ?? 'Failed to load patient demographics');
      }
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  // Load doctor performance
  Future<void> loadDoctorPerformance() async {
    _setLoading(true);
    _setError(null);

    try {
      final response = await _apiService.getDoctorPerformance();

      if (response['success'] == true) {
        _doctorPerformance = response['data'] as Map<String, dynamic>;
      } else {
        _setError(response['error'] ?? 'Failed to load doctor performance');
      }
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  // Load financial summary
  Future<void> loadFinancialSummary({
    String? startDate,
    String? endDate,
  }) async {
    _setLoading(true);
    _setError(null);

    try {
      final response = await _apiService.getFinancialSummary(
        startDate: startDate,
        endDate: endDate,
      );

      if (response['success'] == true) {
        _financialSummary = response['data'] as Map<String, dynamic>;
      } else {
        _setError(response['error'] ?? 'Failed to load financial summary');
      }
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  // Load appointment trends
  Future<void> loadAppointmentTrends() async {
    _setLoading(true);
    _setError(null);

    try {
      final response = await _apiService.getAppointmentTrends();

      if (response['success'] == true) {
        _appointmentTrends = response['data'] as Map<String, dynamic>;
      } else {
        _setError(response['error'] ?? 'Failed to load appointment trends');
      }
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  // Load all analytics data
  Future<void> loadAllAnalytics() async {
    await Future.wait([
      loadDashboardAnalytics(),
      loadPatientDemographics(),
      loadDoctorPerformance(),
      loadFinancialSummary(),
      loadAppointmentTrends(),
    ]);
  }

  // Clear error
  void clearError() {
    _setError(null);
  }

  // Private helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }

  // Refresh all data
  Future<void> refresh() async {
    await loadAllAnalytics();
  }
}
