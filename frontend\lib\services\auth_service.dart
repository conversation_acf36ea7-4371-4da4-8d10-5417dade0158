import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user.dart';

class AuthService {
  static const String baseUrl = 'http://localhost:3000/api/auth';
  
  final http.Client _client = http.Client();

  // Helper method to get stored token
  Future<String?> getToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('auth_token');
  }

  // Helper method to store token
  Future<void> _storeToken(String token) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('auth_token', token);
  }

  // Helper method to remove token
  Future<void> _removeToken() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('auth_token');
  }

  // Helper method to get headers with auth token
  Future<Map<String, String>> _getHeaders({bool includeAuth = false}) async {
    final headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };

    if (includeAuth) {
      final token = await getToken();
      if (token != null) {
        headers['Authorization'] = 'Bearer $token';
      }
    }

    return headers;
  }

  // Helper method to handle HTTP requests
  Future<Map<String, dynamic>> _makeRequest(
    String method,
    String endpoint, {
    Map<String, dynamic>? body,
    bool includeAuth = false,
  }) async {
    try {
      final uri = Uri.parse('$baseUrl$endpoint');
      final headers = await _getHeaders(includeAuth: includeAuth);

      late http.Response response;

      switch (method.toUpperCase()) {
        case 'GET':
          response = await _client.get(uri, headers: headers);
          break;
        case 'POST':
          response = await _client.post(
            uri,
            headers: headers,
            body: body != null ? jsonEncode(body) : null,
          );
          break;
        case 'PUT':
          response = await _client.put(
            uri,
            headers: headers,
            body: body != null ? jsonEncode(body) : null,
          );
          break;
        default:
          throw Exception('Unsupported HTTP method: $method');
      }

      final responseData = jsonDecode(response.body) as Map<String, dynamic>;

      if (response.statusCode >= 200 && response.statusCode < 300) {
        return responseData;
      } else {
        throw AuthException(
          message: responseData['error'] ?? 'Unknown error occurred',
          statusCode: response.statusCode,
          details: responseData['details'],
        );
      }
    } catch (e) {
      if (e is AuthException) rethrow;
      throw AuthException(
        message: 'Network error: ${e.toString()}',
        statusCode: 0,
      );
    }
  }

  // Login
  Future<AuthResult> login(String username, String password) async {
    try {
      final response = await _makeRequest('POST', '/login', body: {
        'username': username,
        'password': password,
      });

      if (response['success'] == true) {
        final data = response['data'] as Map<String, dynamic>;
        final user = User.fromJson(data['user'] as Map<String, dynamic>);
        final token = data['token'] as String;

        await _storeToken(token);

        return AuthResult(
          success: true,
          user: user,
          token: token,
          message: response['message'] as String?,
        );
      } else {
        return AuthResult(
          success: false,
          error: response['error'] as String?,
        );
      }
    } catch (e) {
      return AuthResult(
        success: false,
        error: e.toString(),
      );
    }
  }

  // Register
  Future<AuthResult> register({
    required String username,
    required String email,
    required String password,
    required String firstName,
    required String lastName,
    String role = 'staff',
  }) async {
    try {
      final response = await _makeRequest('POST', '/register', body: {
        'username': username,
        'email': email,
        'password': password,
        'first_name': firstName,
        'last_name': lastName,
        'role': role,
      });

      if (response['success'] == true) {
        final data = response['data'] as Map<String, dynamic>;
        final user = User.fromJson(data['user'] as Map<String, dynamic>);
        final token = data['token'] as String;

        await _storeToken(token);

        return AuthResult(
          success: true,
          user: user,
          token: token,
          message: response['message'] as String?,
        );
      } else {
        return AuthResult(
          success: false,
          error: response['error'] as String?,
        );
      }
    } catch (e) {
      return AuthResult(
        success: false,
        error: e.toString(),
      );
    }
  }

  // Get current user profile
  Future<User?> getCurrentUser() async {
    try {
      final response = await _makeRequest('GET', '/me', includeAuth: true);

      if (response['success'] == true) {
        return User.fromJson(response['data'] as Map<String, dynamic>);
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  // Update profile
  Future<AuthResult> updateProfile({
    String? firstName,
    String? lastName,
    String? email,
  }) async {
    try {
      final body = <String, dynamic>{};
      if (firstName != null) body['first_name'] = firstName;
      if (lastName != null) body['last_name'] = lastName;
      if (email != null) body['email'] = email;

      final response = await _makeRequest('PUT', '/profile', 
          body: body, includeAuth: true);

      if (response['success'] == true) {
        final user = User.fromJson(response['data'] as Map<String, dynamic>);
        return AuthResult(
          success: true,
          user: user,
          message: response['message'] as String?,
        );
      } else {
        return AuthResult(
          success: false,
          error: response['error'] as String?,
        );
      }
    } catch (e) {
      return AuthResult(
        success: false,
        error: e.toString(),
      );
    }
  }

  // Change password
  Future<AuthResult> changePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    try {
      final response = await _makeRequest('POST', '/change-password', body: {
        'current_password': currentPassword,
        'new_password': newPassword,
      }, includeAuth: true);

      if (response['success'] == true) {
        return AuthResult(
          success: true,
          message: response['message'] as String?,
        );
      } else {
        return AuthResult(
          success: false,
          error: response['error'] as String?,
        );
      }
    } catch (e) {
      return AuthResult(
        success: false,
        error: e.toString(),
      );
    }
  }

  // Logout
  Future<void> logout() async {
    try {
      await _makeRequest('POST', '/logout', includeAuth: true);
    } catch (e) {
      // Continue with logout even if API call fails
    } finally {
      await _removeToken();
    }
  }

  // Check if user is logged in
  Future<bool> isLoggedIn() async {
    final token = await getToken();
    if (token == null) return false;

    // Optionally verify token with server
    try {
      final user = await getCurrentUser();
      return user != null;
    } catch (e) {
      return false;
    }
  }

  void dispose() {
    _client.close();
  }
}

class AuthResult {
  final bool success;
  final User? user;
  final String? token;
  final String? message;
  final String? error;

  AuthResult({
    required this.success,
    this.user,
    this.token,
    this.message,
    this.error,
  });
}

class AuthException implements Exception {
  final String message;
  final int statusCode;
  final dynamic details;

  AuthException({
    required this.message,
    required this.statusCode,
    this.details,
  });

  @override
  String toString() {
    return 'AuthException: $message (Status: $statusCode)';
  }
}
