const sqlite3 = require('sqlite3').verbose();
const fs = require('fs');
const path = require('path');

async function initializeDatabase() {
  const dbPath = path.join(__dirname, 'hospital.db');
  
  // Remove existing database
  if (fs.existsSync(dbPath)) {
    fs.unlinkSync(dbPath);
    console.log('Removed existing database');
  }

  const db = new sqlite3.Database(dbPath);
  
  return new Promise((resolve, reject) => {
    db.serialize(() => {
      // Enable foreign keys
      db.run('PRAGMA foreign_keys = ON');

      // Create departments table
      db.run(`
        CREATE TABLE departments (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name VARCHAR(100) NOT NULL UNIQUE,
          description TEXT,
          head_doctor_id INTEGER,
          budget DECIMAL(15,2) CHECK (budget >= 0),
          established_date DATE,
          phone VARCHAR(20),
          email VARCHAR(100),
          location VARCHAR(200),
          status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `);

      // Create doctors table
      db.run(`
        CREATE TABLE doctors (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          first_name VARCHAR(50) NOT NULL,
          last_name VARCHAR(50) NOT NULL,
          email VARCHAR(100) UNIQUE,
          phone VARCHAR(20),
          license_number VARCHAR(50) UNIQUE NOT NULL,
          specialization VARCHAR(100) NOT NULL,
          department_id INTEGER,
          hire_date DATE NOT NULL,
          salary DECIMAL(10,2) CHECK (salary >= 0),
          experience_years INTEGER CHECK (experience_years >= 0),
          qualification VARCHAR(200),
          address TEXT,
          date_of_birth DATE,
          gender VARCHAR(10) CHECK (gender IN ('Male', 'Female', 'Other')),
          status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'on_leave')),
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE SET NULL
        )
      `);

      // Create patients table
      db.run(`
        CREATE TABLE patients (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          first_name VARCHAR(50) NOT NULL,
          last_name VARCHAR(50) NOT NULL,
          email VARCHAR(100),
          phone VARCHAR(20),
          date_of_birth DATE NOT NULL,
          gender VARCHAR(10) CHECK (gender IN ('Male', 'Female', 'Other')),
          address TEXT,
          emergency_contact_name VARCHAR(100),
          emergency_contact_phone VARCHAR(20),
          blood_type VARCHAR(5) CHECK (blood_type IN ('A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-')),
          allergies TEXT,
          medical_history TEXT,
          insurance_number VARCHAR(50),
          insurance_provider VARCHAR(100),
          registration_date DATE DEFAULT (date('now')),
          status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'deceased')),
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `);

      // Create appointments table
      db.run(`
        CREATE TABLE appointments (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          patient_id INTEGER NOT NULL,
          doctor_id INTEGER NOT NULL,
          appointment_date DATE NOT NULL,
          appointment_time TIME NOT NULL,
          duration_minutes INTEGER DEFAULT 30 CHECK (duration_minutes > 0),
          reason VARCHAR(500),
          notes TEXT,
          status VARCHAR(20) DEFAULT 'scheduled' CHECK (status IN ('scheduled', 'confirmed', 'completed', 'cancelled', 'no_show')),
          appointment_type VARCHAR(50) DEFAULT 'consultation' CHECK (appointment_type IN ('consultation', 'follow_up', 'emergency', 'surgery', 'checkup')),
          priority VARCHAR(10) DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'urgent')),
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (patient_id) REFERENCES patients(id) ON DELETE CASCADE,
          FOREIGN KEY (doctor_id) REFERENCES doctors(id) ON DELETE CASCADE,
          UNIQUE(doctor_id, appointment_date, appointment_time)
        )
      `);

      // Create prescriptions table
      db.run(`
        CREATE TABLE prescriptions (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          patient_id INTEGER NOT NULL,
          doctor_id INTEGER NOT NULL,
          appointment_id INTEGER,
          medication_name VARCHAR(200) NOT NULL,
          dosage VARCHAR(100) NOT NULL,
          frequency VARCHAR(100) NOT NULL,
          duration_days INTEGER CHECK (duration_days > 0),
          instructions TEXT,
          prescribed_date DATE DEFAULT (date('now')),
          start_date DATE,
          end_date DATE,
          status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'completed', 'discontinued')),
          refills_remaining INTEGER DEFAULT 0 CHECK (refills_remaining >= 0),
          pharmacy_notes TEXT,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (patient_id) REFERENCES patients(id) ON DELETE CASCADE,
          FOREIGN KEY (doctor_id) REFERENCES doctors(id) ON DELETE CASCADE,
          FOREIGN KEY (appointment_id) REFERENCES appointments(id) ON DELETE SET NULL
        )
      `);

      // Create billing table
      db.run(`
        CREATE TABLE billing (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          patient_id INTEGER NOT NULL,
          appointment_id INTEGER,
          invoice_number VARCHAR(50) UNIQUE NOT NULL,
          total_amount DECIMAL(10,2) NOT NULL CHECK (total_amount >= 0),
          paid_amount DECIMAL(10,2) DEFAULT 0 CHECK (paid_amount >= 0),
          balance_amount DECIMAL(10,2) GENERATED ALWAYS AS (total_amount - paid_amount) STORED,
          billing_date DATE DEFAULT (date('now')),
          due_date DATE,
          payment_status VARCHAR(20) DEFAULT 'pending' CHECK (payment_status IN ('pending', 'partial', 'paid', 'overdue', 'cancelled')),
          payment_method VARCHAR(50) CHECK (payment_method IN ('cash', 'card', 'insurance', 'bank_transfer', 'check')),
          insurance_claim_amount DECIMAL(10,2) DEFAULT 0 CHECK (insurance_claim_amount >= 0),
          discount_amount DECIMAL(10,2) DEFAULT 0 CHECK (discount_amount >= 0),
          tax_amount DECIMAL(10,2) DEFAULT 0 CHECK (tax_amount >= 0),
          description TEXT,
          notes TEXT,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (patient_id) REFERENCES patients(id) ON DELETE CASCADE,
          FOREIGN KEY (appointment_id) REFERENCES appointments(id) ON DELETE SET NULL
        )
      `);

      // Create indexes
      db.run('CREATE INDEX idx_doctors_department_id ON doctors(department_id)');
      db.run('CREATE INDEX idx_doctors_specialization ON doctors(specialization)');
      db.run('CREATE INDEX idx_doctors_status ON doctors(status)');
      
      db.run('CREATE INDEX idx_patients_email ON patients(email)');
      db.run('CREATE INDEX idx_patients_phone ON patients(phone)');
      db.run('CREATE INDEX idx_patients_status ON patients(status)');
      db.run('CREATE INDEX idx_patients_registration_date ON patients(registration_date)');
      
      db.run('CREATE INDEX idx_appointments_patient_id ON appointments(patient_id)');
      db.run('CREATE INDEX idx_appointments_doctor_id ON appointments(doctor_id)');
      db.run('CREATE INDEX idx_appointments_date ON appointments(appointment_date)');
      db.run('CREATE INDEX idx_appointments_status ON appointments(status)');
      db.run('CREATE INDEX idx_appointments_date_time ON appointments(appointment_date, appointment_time)');
      
      db.run('CREATE INDEX idx_prescriptions_patient_id ON prescriptions(patient_id)');
      db.run('CREATE INDEX idx_prescriptions_doctor_id ON prescriptions(doctor_id)');
      db.run('CREATE INDEX idx_prescriptions_appointment_id ON prescriptions(appointment_id)');
      db.run('CREATE INDEX idx_prescriptions_prescribed_date ON prescriptions(prescribed_date)');
      
      db.run('CREATE INDEX idx_billing_patient_id ON billing(patient_id)');
      db.run('CREATE INDEX idx_billing_appointment_id ON billing(appointment_id)');
      db.run('CREATE INDEX idx_billing_payment_status ON billing(payment_status)');
      db.run('CREATE INDEX idx_billing_billing_date ON billing(billing_date)');
      db.run('CREATE INDEX idx_billing_due_date ON billing(due_date)', (err) => {
        if (err) {
          reject(err);
        } else {
          console.log('Database schema created successfully');
          db.close();
          resolve();
        }
      });
    });
  });
}

if (require.main === module) {
  initializeDatabase()
    .then(() => {
      console.log('Database initialization complete');
      process.exit(0);
    })
    .catch(err => {
      console.error('Database initialization failed:', err);
      process.exit(1);
    });
}

module.exports = initializeDatabase;
