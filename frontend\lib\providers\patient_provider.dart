import 'package:flutter/foundation.dart';
import '../models/patient.dart';
import '../services/api_service.dart';

class PatientProvider extends ChangeNotifier {
  final ApiService _apiService;

  PatientProvider(this._apiService);

  List<Patient> _patients = [];
  Patient? _selectedPatient;
  bool _isLoading = false;
  String? _error;
  int _currentPage = 1;
  bool _hasMoreData = true;

  // Getters
  List<Patient> get patients => _patients;
  Patient? get selectedPatient => _selectedPatient;
  bool get isLoading => _isLoading;
  String? get error => _error;
  int get currentPage => _currentPage;
  bool get hasMoreData => _hasMoreData;

  // Load patients with pagination and filters
  Future<void> loadPatients({
    bool refresh = false,
    String? status,
    String? search,
    String? bloodType,
  }) async {
    if (refresh) {
      _currentPage = 1;
      _patients.clear();
      _hasMoreData = true;
    }

    if (_isLoading || !_hasMoreData) return;

    _setLoading(true);
    _setError(null);

    try {
      final response = await _apiService.getPatients(
        page: _currentPage,
        limit: 20,
        status: status,
        search: search,
        bloodType: bloodType,
      );

      if (response['success'] == true) {
        final List<dynamic> patientData = response['data'] as List<dynamic>;
        final List<Patient> newPatients = patientData
            .map((json) => Patient.fromJson(json as Map<String, dynamic>))
            .toList();

        if (refresh) {
          _patients = newPatients;
        } else {
          _patients.addAll(newPatients);
        }

        _currentPage++;
        _hasMoreData = newPatients.length == 20; // Assuming limit is 20
      } else {
        _setError(response['error'] ?? 'Failed to load patients');
      }
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  // Load a specific patient
  Future<void> loadPatient(int id) async {
    _setLoading(true);
    _setError(null);

    try {
      final response = await _apiService.getPatient(id);

      if (response['success'] == true) {
        _selectedPatient = Patient.fromJson(response['data'] as Map<String, dynamic>);
      } else {
        _setError(response['error'] ?? 'Failed to load patient');
      }
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  // Create a new patient
  Future<bool> createPatient(Map<String, dynamic> patientData) async {
    _setLoading(true);
    _setError(null);

    try {
      final response = await _apiService.createPatient(patientData);

      if (response['success'] == true) {
        final newPatient = Patient.fromJson(response['data'] as Map<String, dynamic>);
        _patients.insert(0, newPatient);
        notifyListeners();
        return true;
      } else {
        _setError(response['error'] ?? 'Failed to create patient');
        return false;
      }
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Update a patient
  Future<bool> updatePatient(int id, Map<String, dynamic> patientData) async {
    _setLoading(true);
    _setError(null);

    try {
      final response = await _apiService.updatePatient(id, patientData);

      if (response['success'] == true) {
        final updatedPatient = Patient.fromJson(response['data'] as Map<String, dynamic>);
        
        // Update in the list
        final index = _patients.indexWhere((p) => p.id == id);
        if (index != -1) {
          _patients[index] = updatedPatient;
        }

        // Update selected patient if it's the same
        if (_selectedPatient?.id == id) {
          _selectedPatient = updatedPatient;
        }

        notifyListeners();
        return true;
      } else {
        _setError(response['error'] ?? 'Failed to update patient');
        return false;
      }
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Delete a patient
  Future<bool> deletePatient(int id) async {
    _setLoading(true);
    _setError(null);

    try {
      final response = await _apiService.deletePatient(id);

      if (response['success'] == true) {
        _patients.removeWhere((p) => p.id == id);
        
        if (_selectedPatient?.id == id) {
          _selectedPatient = null;
        }

        notifyListeners();
        return true;
      } else {
        _setError(response['error'] ?? 'Failed to delete patient');
        return false;
      }
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Search patients
  Future<void> searchPatients(String query) async {
    await loadPatients(refresh: true, search: query);
  }

  // Filter patients by status
  Future<void> filterByStatus(String status) async {
    await loadPatients(refresh: true, status: status);
  }

  // Filter patients by blood type
  Future<void> filterByBloodType(String bloodType) async {
    await loadPatients(refresh: true, bloodType: bloodType);
  }

  // Clear selected patient
  void clearSelectedPatient() {
    _selectedPatient = null;
    notifyListeners();
  }

  // Clear error
  void clearError() {
    _setError(null);
  }

  // Private helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }

  // Refresh data
  Future<void> refresh() async {
    await loadPatients(refresh: true);
  }
}
