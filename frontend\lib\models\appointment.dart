class Appointment {
  final int id;
  final int patientId;
  final int doctorId;
  final DateTime appointmentDate;
  final String appointmentTime;
  final int durationMinutes;
  final String? reason;
  final String? notes;
  final String status;
  final String appointmentType;
  final String priority;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String? patientFirstName;
  final String? patientLastName;
  final String? patientPhone;
  final String? patientEmail;
  final String? doctorFirstName;
  final String? doctorLastName;
  final String? specialization;
  final String? departmentName;

  Appointment({
    required this.id,
    required this.patientId,
    required this.doctorId,
    required this.appointmentDate,
    required this.appointmentTime,
    required this.durationMinutes,
    this.reason,
    this.notes,
    required this.status,
    required this.appointmentType,
    required this.priority,
    required this.createdAt,
    required this.updatedAt,
    this.patientFirstName,
    this.patientLastName,
    this.patientPhone,
    this.patientEmail,
    this.doctorFirstName,
    this.doctorLastName,
    this.specialization,
    this.departmentName,
  });

  String get patientFullName {
    if (patientFirstName != null && patientLastName != null) {
      return '$patientFirstName $patientLastName';
    }
    return 'Unknown Patient';
  }

  String get doctorFullName {
    if (doctorFirstName != null && doctorLastName != null) {
      return '$doctorFirstName $doctorLastName';
    }
    return 'Unknown Doctor';
  }

  DateTime get appointmentDateTime {
    final timeParts = appointmentTime.split(':');
    final hour = int.parse(timeParts[0]);
    final minute = int.parse(timeParts[1]);
    
    return DateTime(
      appointmentDate.year,
      appointmentDate.month,
      appointmentDate.day,
      hour,
      minute,
    );
  }

  bool get isToday {
    final now = DateTime.now();
    return appointmentDate.year == now.year &&
           appointmentDate.month == now.month &&
           appointmentDate.day == now.day;
  }

  bool get isPast {
    return appointmentDateTime.isBefore(DateTime.now());
  }

  bool get isUpcoming {
    return appointmentDateTime.isAfter(DateTime.now());
  }

  factory Appointment.fromJson(Map<String, dynamic> json) {
    return Appointment(
      id: json['id'] as int,
      patientId: json['patient_id'] as int,
      doctorId: json['doctor_id'] as int,
      appointmentDate: DateTime.parse(json['appointment_date'] as String),
      appointmentTime: json['appointment_time'] as String,
      durationMinutes: json['duration_minutes'] as int,
      reason: json['reason'] as String?,
      notes: json['notes'] as String?,
      status: json['status'] as String,
      appointmentType: json['appointment_type'] as String,
      priority: json['priority'] as String,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      patientFirstName: json['patient_first_name'] as String?,
      patientLastName: json['patient_last_name'] as String?,
      patientPhone: json['patient_phone'] as String?,
      patientEmail: json['patient_email'] as String?,
      doctorFirstName: json['doctor_first_name'] as String?,
      doctorLastName: json['doctor_last_name'] as String?,
      specialization: json['specialization'] as String?,
      departmentName: json['department_name'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'patient_id': patientId,
      'doctor_id': doctorId,
      'appointment_date': appointmentDate.toIso8601String().split('T')[0],
      'appointment_time': appointmentTime,
      'duration_minutes': durationMinutes,
      'reason': reason,
      'notes': notes,
      'status': status,
      'appointment_type': appointmentType,
      'priority': priority,
    };
  }

  Appointment copyWith({
    int? id,
    int? patientId,
    int? doctorId,
    DateTime? appointmentDate,
    String? appointmentTime,
    int? durationMinutes,
    String? reason,
    String? notes,
    String? status,
    String? appointmentType,
    String? priority,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? patientFirstName,
    String? patientLastName,
    String? patientPhone,
    String? patientEmail,
    String? doctorFirstName,
    String? doctorLastName,
    String? specialization,
    String? departmentName,
  }) {
    return Appointment(
      id: id ?? this.id,
      patientId: patientId ?? this.patientId,
      doctorId: doctorId ?? this.doctorId,
      appointmentDate: appointmentDate ?? this.appointmentDate,
      appointmentTime: appointmentTime ?? this.appointmentTime,
      durationMinutes: durationMinutes ?? this.durationMinutes,
      reason: reason ?? this.reason,
      notes: notes ?? this.notes,
      status: status ?? this.status,
      appointmentType: appointmentType ?? this.appointmentType,
      priority: priority ?? this.priority,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      patientFirstName: patientFirstName ?? this.patientFirstName,
      patientLastName: patientLastName ?? this.patientLastName,
      patientPhone: patientPhone ?? this.patientPhone,
      patientEmail: patientEmail ?? this.patientEmail,
      doctorFirstName: doctorFirstName ?? this.doctorFirstName,
      doctorLastName: doctorLastName ?? this.doctorLastName,
      specialization: specialization ?? this.specialization,
      departmentName: departmentName ?? this.departmentName,
    );
  }
}
