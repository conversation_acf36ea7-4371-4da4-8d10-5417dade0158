import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../providers/patient_provider.dart';

class PatientDetailScreen extends StatefulWidget {
  final int patientId;

  const PatientDetailScreen({
    super.key,
    required this.patientId,
  });

  @override
  State<PatientDetailScreen> createState() => _PatientDetailScreenState();
}

class _PatientDetailScreenState extends State<PatientDetailScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<PatientProvider>(context, listen: false).loadPatient(widget.patientId);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Patient Details'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () {
              // TODO: Navigate to edit patient screen
            },
          ),
        ],
      ),
      body: Consumer<PatientProvider>(
        builder: (context, patientProvider, child) {
          if (patientProvider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (patientProvider.error != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64.sp,
                    color: Colors.red,
                  ),
                  SizedBox(height: 16.h),
                  Text(
                    'Error: ${patientProvider.error}',
                    style: TextStyle(fontSize: 16.sp),
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(height: 16.h),
                  ElevatedButton(
                    onPressed: () {
                      patientProvider.clearError();
                      patientProvider.loadPatient(widget.patientId);
                    },
                    child: const Text('Retry'),
                  ),
                ],
              ),
            );
          }

          final patient = patientProvider.selectedPatient;
          if (patient == null) {
            return const Center(
              child: Text('Patient not found'),
            );
          }

          return SingleChildScrollView(
            padding: EdgeInsets.all(16.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Patient header
                Card(
                  child: Padding(
                    padding: EdgeInsets.all(20.w),
                    child: Row(
                      children: [
                        CircleAvatar(
                          radius: 40.r,
                          backgroundColor: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                          child: Text(
                            patient.firstName[0].toUpperCase(),
                            style: TextStyle(
                              fontSize: 32.sp,
                              fontWeight: FontWeight.bold,
                              color: Theme.of(context).primaryColor,
                            ),
                          ),
                        ),
                        SizedBox(width: 16.w),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                patient.fullName,
                                style: TextStyle(
                                  fontSize: 24.sp,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              SizedBox(height: 4.h),
                              Text(
                                'Age: ${patient.age} • ${patient.gender}',
                                style: TextStyle(
                                  fontSize: 16.sp,
                                  color: Colors.grey[600],
                                ),
                              ),
                              SizedBox(height: 8.h),
                              Container(
                                padding: EdgeInsets.symmetric(
                                  horizontal: 12.w,
                                  vertical: 6.h,
                                ),
                                decoration: BoxDecoration(
                                  color: _getStatusColor(patient.status).withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(6.r),
                                ),
                                child: Text(
                                  patient.status.toUpperCase(),
                                  style: TextStyle(
                                    fontSize: 12.sp,
                                    fontWeight: FontWeight.w600,
                                    color: _getStatusColor(patient.status),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                SizedBox(height: 16.h),

                // Contact Information
                _buildInfoSection(
                  'Contact Information',
                  [
                    if (patient.email != null) _buildInfoRow('Email', patient.email!),
                    if (patient.phone != null) _buildInfoRow('Phone', patient.phone!),
                    if (patient.address != null) _buildInfoRow('Address', patient.address!),
                  ],
                ),

                SizedBox(height: 16.h),

                // Medical Information
                _buildInfoSection(
                  'Medical Information',
                  [
                    if (patient.bloodType != null) _buildInfoRow('Blood Type', patient.bloodType!),
                    if (patient.allergies != null) _buildInfoRow('Allergies', patient.allergies!),
                    if (patient.medicalHistory != null) _buildInfoRow('Medical History', patient.medicalHistory!),
                  ],
                ),

                SizedBox(height: 16.h),

                // Insurance Information
                if (patient.insuranceProvider != null || patient.insuranceNumber != null)
                  _buildInfoSection(
                    'Insurance Information',
                    [
                      if (patient.insuranceProvider != null) 
                        _buildInfoRow('Provider', patient.insuranceProvider!),
                      if (patient.insuranceNumber != null) 
                        _buildInfoRow('Policy Number', patient.insuranceNumber!),
                    ],
                  ),

                SizedBox(height: 16.h),

                // Emergency Contact
                if (patient.emergencyContactName != null || patient.emergencyContactPhone != null)
                  _buildInfoSection(
                    'Emergency Contact',
                    [
                      if (patient.emergencyContactName != null) 
                        _buildInfoRow('Name', patient.emergencyContactName!),
                      if (patient.emergencyContactPhone != null) 
                        _buildInfoRow('Phone', patient.emergencyContactPhone!),
                    ],
                  ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildInfoSection(String title, List<Widget> children) {
    if (children.isEmpty) return const SizedBox.shrink();
    
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: TextStyle(
                fontSize: 18.sp,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 12.h),
            ...children,
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.only(bottom: 8.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120.w,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 14.sp,
                fontWeight: FontWeight.w600,
                color: Colors.grey[600],
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: 14.sp,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'active':
        return Colors.green;
      case 'inactive':
        return Colors.orange;
      case 'deceased':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }
}
