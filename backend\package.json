{"name": "hospital-management-backend", "version": "1.0.0", "description": "Hospital Management System Backend API", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "seed": "node db/seed.js", "setup": "node db/setup.js"}, "keywords": ["hospital", "management", "api", "nodejs", "express", "sqlite"], "author": "HMS Team", "license": "MIT", "dependencies": {"bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "sqlite3": "^5.1.6"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0"}}