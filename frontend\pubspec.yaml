name: hospital_management_frontend
description: Hospital Management System Flutter Frontend
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter

  # HTTP client for API calls
  http: ^1.1.0

  # State management
  provider: ^6.0.5

  # Navigation
  go_router: ^10.1.2

  # UI components
  cupertino_icons: ^1.0.6
  material_design_icons_flutter: ^5.0.6996

  # Charts for analytics
  fl_chart: ^0.63.0

  # Date/time utilities
  intl: ^0.18.1

  # Local storage
  shared_preferences: ^2.2.2

  # Loading indicators
  flutter_spinkit: ^5.2.0

  # Form validation
  form_field_validator: ^1.1.0

  # Image handling
  cached_network_image: ^3.2.3

  # Responsive design
  flutter_screenutil: ^5.9.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0

flutter:
  uses-material-design: true
  
  assets:
    - assets/images/
    - assets/icons/
  
  fonts:
    - family: Roboto
      fonts:
        - asset: fonts/Roboto-Regular.ttf
        - asset: fonts/Roboto-Bold.ttf
          weight: 700
