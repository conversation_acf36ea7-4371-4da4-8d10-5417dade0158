class Patient {
  final int id;
  final String firstName;
  final String lastName;
  final String? email;
  final String? phone;
  final DateTime dateOfBirth;
  final String gender;
  final String? address;
  final String? emergencyContactName;
  final String? emergencyContactPhone;
  final String? bloodType;
  final String? allergies;
  final String? medicalHistory;
  final String? insuranceNumber;
  final String? insuranceProvider;
  final DateTime? registrationDate;
  final String status;
  final DateTime createdAt;
  final DateTime updatedAt;
  final int? appointmentCount;
  final DateTime? lastAppointmentDate;
  final double? outstandingBalance;

  Patient({
    required this.id,
    required this.firstName,
    required this.lastName,
    this.email,
    this.phone,
    required this.dateOfBirth,
    required this.gender,
    this.address,
    this.emergencyContactName,
    this.emergencyContactPhone,
    this.bloodType,
    this.allergies,
    this.medicalHistory,
    this.insuranceNumber,
    this.insuranceProvider,
    this.registrationDate,
    required this.status,
    required this.createdAt,
    required this.updatedAt,
    this.appointmentCount,
    this.lastAppointmentDate,
    this.outstandingBalance,
  });

  String get fullName => '$firstName $lastName';

  int get age {
    final now = DateTime.now();
    int age = now.year - dateOfBirth.year;
    if (now.month < dateOfBirth.month ||
        (now.month == dateOfBirth.month && now.day < dateOfBirth.day)) {
      age--;
    }
    return age;
  }

  factory Patient.fromJson(Map<String, dynamic> json) {
    return Patient(
      id: json['id'] as int,
      firstName: json['first_name'] as String,
      lastName: json['last_name'] as String,
      email: json['email'] as String?,
      phone: json['phone'] as String?,
      dateOfBirth: DateTime.parse(json['date_of_birth'] as String),
      gender: json['gender'] as String,
      address: json['address'] as String?,
      emergencyContactName: json['emergency_contact_name'] as String?,
      emergencyContactPhone: json['emergency_contact_phone'] as String?,
      bloodType: json['blood_type'] as String?,
      allergies: json['allergies'] as String?,
      medicalHistory: json['medical_history'] as String?,
      insuranceNumber: json['insurance_number'] as String?,
      insuranceProvider: json['insurance_provider'] as String?,
      registrationDate: json['registration_date'] != null
          ? DateTime.parse(json['registration_date'] as String)
          : null,
      status: json['status'] as String,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      appointmentCount: json['appointment_count'] as int?,
      lastAppointmentDate: json['last_appointment_date'] != null
          ? DateTime.parse(json['last_appointment_date'] as String)
          : null,
      outstandingBalance: json['outstanding_balance']?.toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'first_name': firstName,
      'last_name': lastName,
      'email': email,
      'phone': phone,
      'date_of_birth': dateOfBirth.toIso8601String().split('T')[0],
      'gender': gender,
      'address': address,
      'emergency_contact_name': emergencyContactName,
      'emergency_contact_phone': emergencyContactPhone,
      'blood_type': bloodType,
      'allergies': allergies,
      'medical_history': medicalHistory,
      'insurance_number': insuranceNumber,
      'insurance_provider': insuranceProvider,
      'registration_date': registrationDate?.toIso8601String().split('T')[0],
      'status': status,
    };
  }

  Patient copyWith({
    int? id,
    String? firstName,
    String? lastName,
    String? email,
    String? phone,
    DateTime? dateOfBirth,
    String? gender,
    String? address,
    String? emergencyContactName,
    String? emergencyContactPhone,
    String? bloodType,
    String? allergies,
    String? medicalHistory,
    String? insuranceNumber,
    String? insuranceProvider,
    DateTime? registrationDate,
    String? status,
    DateTime? createdAt,
    DateTime? updatedAt,
    int? appointmentCount,
    DateTime? lastAppointmentDate,
    double? outstandingBalance,
  }) {
    return Patient(
      id: id ?? this.id,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      gender: gender ?? this.gender,
      address: address ?? this.address,
      emergencyContactName: emergencyContactName ?? this.emergencyContactName,
      emergencyContactPhone: emergencyContactPhone ?? this.emergencyContactPhone,
      bloodType: bloodType ?? this.bloodType,
      allergies: allergies ?? this.allergies,
      medicalHistory: medicalHistory ?? this.medicalHistory,
      insuranceNumber: insuranceNumber ?? this.insuranceNumber,
      insuranceProvider: insuranceProvider ?? this.insuranceProvider,
      registrationDate: registrationDate ?? this.registrationDate,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      appointmentCount: appointmentCount ?? this.appointmentCount,
      lastAppointmentDate: lastAppointmentDate ?? this.lastAppointmentDate,
      outstandingBalance: outstandingBalance ?? this.outstandingBalance,
    );
  }
}
