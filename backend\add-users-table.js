const database = require('./db/database');

async function addUsersTable() {
  try {
    await database.init();
    
    console.log('Adding users table...');
    
    await database.run(`
      CREATE TABLE IF NOT EXISTS users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        username VARCHAR(50) NOT NULL UNIQUE,
        email VARCHAR(100) NOT NULL UNIQUE,
        password_hash VARCHAR(255) NOT NULL,
        role VARCHAR(20) DEFAULT 'staff' CHECK (role IN ('admin', 'doctor', 'nurse', 'staff', 'receptionist')),
        first_name <PERSON><PERSON><PERSON><PERSON>(50) NOT NULL,
        last_name <PERSON><PERSON><PERSON><PERSON>(50) NOT NULL,
        is_active BOOLEAN DEFAULT 1,
        last_login DATETIME,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);
    
    console.log('Users table created successfully!');
    
    // Add index for username
    await database.run('CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)');
    await database.run('CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)');
    await database.run('CREATE INDEX IF NOT EXISTS idx_users_role ON users(role)');
    
    console.log('Users table indexes created successfully!');
    
    database.close();
    process.exit(0);
  } catch (error) {
    console.error('Error adding users table:', error);
    process.exit(1);
  }
}

addUsersTable();
