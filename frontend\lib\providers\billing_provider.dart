import 'package:flutter/foundation.dart';
import '../services/api_service.dart';

class BillingProvider extends ChangeNotifier {
  final ApiService _apiService;

  BillingProvider(this._apiService);

  List<Map<String, dynamic>> _billingRecords = [];
  Map<String, dynamic>? _selectedBilling;
  bool _isLoading = false;
  String? _error;
  int _currentPage = 1;
  bool _hasMoreData = true;

  // Getters
  List<Map<String, dynamic>> get billingRecords => _billingRecords;
  Map<String, dynamic>? get selectedBilling => _selectedBilling;
  bool get isLoading => _isLoading;
  String? get error => _error;
  int get currentPage => _currentPage;
  bool get hasMoreData => _hasMoreData;

  // Load billing records with pagination and filters
  Future<void> loadBillingRecords({
    bool refresh = false,
    String? paymentStatus,
    int? patientId,
    String? dateFrom,
    String? dateTo,
    bool? overdue,
  }) async {
    if (refresh) {
      _currentPage = 1;
      _billingRecords.clear();
      _hasMoreData = true;
    }

    if (_isLoading || !_hasMoreData) return;

    _setLoading(true);
    _setError(null);

    try {
      final response = await _apiService.getBilling(
        page: _currentPage,
        limit: 20,
        paymentStatus: paymentStatus,
        patientId: patientId,
        dateFrom: dateFrom,
        dateTo: dateTo,
        overdue: overdue,
      );

      if (response['success'] == true) {
        final List<dynamic> billingData = response['data'] as List<dynamic>;
        final List<Map<String, dynamic>> newRecords = billingData
            .map((json) => json as Map<String, dynamic>)
            .toList();

        if (refresh) {
          _billingRecords = newRecords;
        } else {
          _billingRecords.addAll(newRecords);
        }

        _currentPage++;
        _hasMoreData = newRecords.length == 20;
      } else {
        _setError(response['error'] ?? 'Failed to load billing records');
      }
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  // Load a specific billing record
  Future<void> loadBillingRecord(int id) async {
    _setLoading(true);
    _setError(null);

    try {
      final response = await _apiService.getBillingRecord(id);

      if (response['success'] == true) {
        _selectedBilling = response['data'] as Map<String, dynamic>;
      } else {
        _setError(response['error'] ?? 'Failed to load billing record');
      }
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  // Process payment
  Future<bool> processPayment(int id, Map<String, dynamic> paymentData) async {
    _setLoading(true);
    _setError(null);

    try {
      final response = await _apiService.processPayment(id, paymentData);

      if (response['success'] == true) {
        final updatedBilling = response['data'] as Map<String, dynamic>;
        
        // Update in the list
        final index = _billingRecords.indexWhere((b) => b['id'] == id);
        if (index != -1) {
          _billingRecords[index] = updatedBilling;
        }

        // Update selected billing if it's the same
        if (_selectedBilling?['id'] == id) {
          _selectedBilling = updatedBilling;
        }

        notifyListeners();
        return true;
      } else {
        _setError(response['error'] ?? 'Failed to process payment');
        return false;
      }
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Filter by payment status
  Future<void> filterByPaymentStatus(String status) async {
    await loadBillingRecords(refresh: true, paymentStatus: status);
  }

  // Filter by patient
  Future<void> filterByPatient(int patientId) async {
    await loadBillingRecords(refresh: true, patientId: patientId);
  }

  // Filter overdue bills
  Future<void> filterOverdue() async {
    await loadBillingRecords(refresh: true, overdue: true);
  }

  // Clear selected billing
  void clearSelectedBilling() {
    _selectedBilling = null;
    notifyListeners();
  }

  // Clear error
  void clearError() {
    _setError(null);
  }

  // Private helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }

  // Refresh data
  Future<void> refresh() async {
    await loadBillingRecords(refresh: true);
  }
}
