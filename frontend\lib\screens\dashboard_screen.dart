import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';

import '../providers/analytics_provider.dart';
import '../providers/appointment_provider.dart';
import '../widgets/dashboard_card.dart';
import '../widgets/quick_stats_card.dart';
import '../widgets/recent_appointments_card.dart';
import '../widgets/navigation_drawer.dart' as custom;
import 'patients/patient_list_screen.dart';
import 'patients/add_patient_screen.dart';
import 'doctors/doctor_list_screen.dart';
import 'appointments/appointment_list_screen.dart';
import 'appointments/book_appointment_screen.dart';
import 'billing/billing_list_screen.dart';
import 'analytics/analytics_screen.dart';

class DashboardScreen extends StatefulWidget {
  const DashboardScreen({super.key});

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadData();
    });
  }

  Future<void> _loadData() async {
    final analyticsProvider = Provider.of<AnalyticsProvider>(context, listen: false);
    final appointmentProvider = Provider.of<AppointmentProvider>(context, listen: false);
    
    await Future.wait([
      analyticsProvider.loadDashboardAnalytics(),
      appointmentProvider.loadTodaysAppointments(),
      appointmentProvider.loadUpcomingAppointments(),
    ]);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Hospital Management System'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
          ),
          IconButton(
            icon: const Icon(Icons.notifications),
            onPressed: () {
              // TODO: Implement notifications
            },
          ),
        ],
      ),
      drawer: const custom.NavigationDrawer(),
      body: RefreshIndicator(
        onRefresh: _loadData,
        child: SingleChildScrollView(
          padding: EdgeInsets.all(16.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Welcome section
              Container(
                width: double.infinity,
                padding: EdgeInsets.all(20.w),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      Theme.of(context).primaryColor,
                      Theme.of(context).primaryColor.withOpacity(0.8),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(12.r),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Welcome to HMS',
                      style: TextStyle(
                        fontSize: 24.sp,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    SizedBox(height: 8.h),
                    Text(
                      'Manage your hospital operations efficiently',
                      style: TextStyle(
                        fontSize: 16.sp,
                        color: Colors.white.withOpacity(0.9),
                      ),
                    ),
                  ],
                ),
              ),
              
              SizedBox(height: 24.h),

              // Quick stats
              Consumer<AnalyticsProvider>(
                builder: (context, analyticsProvider, child) {
                  if (analyticsProvider.isLoading) {
                    return const Center(child: CircularProgressIndicator());
                  }

                  final dashboardData = analyticsProvider.dashboardData;
                  if (dashboardData == null) {
                    return const SizedBox.shrink();
                  }

                  final overview = dashboardData['overview'] as Map<String, dynamic>?;
                  
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Quick Overview',
                        style: TextStyle(
                          fontSize: 20.sp,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(height: 16.h),
                      GridView.count(
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        crossAxisCount: 2,
                        crossAxisSpacing: 16.w,
                        mainAxisSpacing: 16.h,
                        childAspectRatio: 1.5,
                        children: [
                          QuickStatsCard(
                            title: 'Active Patients',
                            value: overview?['active_patients']?.toString() ?? '0',
                            icon: MdiIcons.accountGroup,
                            color: Colors.blue,
                            onTap: () {
                              Navigator.of(context).push(
                                MaterialPageRoute(builder: (context) => const PatientListScreen()),
                              );
                            },
                          ),
                          QuickStatsCard(
                            title: 'Active Doctors',
                            value: overview?['active_doctors']?.toString() ?? '0',
                            icon: MdiIcons.doctor,
                            color: Colors.green,
                            onTap: () {
                              Navigator.of(context).push(
                                MaterialPageRoute(builder: (context) => const DoctorListScreen()),
                              );
                            },
                          ),
                          QuickStatsCard(
                            title: 'Today\'s Appointments',
                            value: overview?['today_appointments']?.toString() ?? '0',
                            icon: MdiIcons.calendar,
                            color: Colors.orange,
                            onTap: () {
                              Navigator.of(context).push(
                                MaterialPageRoute(builder: (context) => const AppointmentListScreen()),
                              );
                            },
                          ),
                          QuickStatsCard(
                            title: 'Outstanding Revenue',
                            value: '\$${overview?['outstanding_revenue']?.toStringAsFixed(0) ?? '0'}',
                            icon: MdiIcons.currencyUsd,
                            color: Colors.red,
                            onTap: () {
                              Navigator.of(context).push(
                                MaterialPageRoute(builder: (context) => const BillingListScreen()),
                              );
                            },
                          ),
                        ],
                      ),
                    ],
                  );
                },
              ),

              SizedBox(height: 24.h),

              // Quick actions
              Text(
                'Quick Actions',
                style: TextStyle(
                  fontSize: 20.sp,
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: 16.h),
              GridView.count(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                crossAxisCount: 2,
                crossAxisSpacing: 16.w,
                mainAxisSpacing: 16.h,
                childAspectRatio: 1.2,
                children: [
                  DashboardCard(
                    title: 'Add Patient',
                    icon: MdiIcons.accountPlus,
                    color: Colors.blue,
                    onTap: () {
                      Navigator.of(context).push(
                        MaterialPageRoute(builder: (context) => const AddPatientScreen()),
                      );
                    },
                  ),
                  DashboardCard(
                    title: 'Book Appointment',
                    icon: MdiIcons.calendarPlus,
                    color: Colors.green,
                    onTap: () {
                      Navigator.of(context).push(
                        MaterialPageRoute(builder: (context) => const BookAppointmentScreen()),
                      );
                    },
                  ),
                  DashboardCard(
                    title: 'View Analytics',
                    icon: MdiIcons.chartLine,
                    color: Colors.purple,
                    onTap: () {
                      Navigator.of(context).push(
                        MaterialPageRoute(builder: (context) => const AnalyticsScreen()),
                      );
                    },
                  ),
                  DashboardCard(
                    title: 'Billing',
                    icon: MdiIcons.receipt,
                    color: Colors.orange,
                    onTap: () {
                      Navigator.of(context).push(
                        MaterialPageRoute(builder: (context) => const BillingListScreen()),
                      );
                    },
                  ),
                ],
              ),

              SizedBox(height: 24.h),

              // Recent appointments
              const RecentAppointmentsCard(),
            ],
          ),
        ),
      ),
    );
  }
}
