import 'package:flutter/foundation.dart';
import '../models/appointment.dart';
import '../services/api_service.dart';

class AppointmentProvider extends ChangeNotifier {
  final ApiService _apiService;

  AppointmentProvider(this._apiService);

  List<Appointment> _appointments = [];
  List<Appointment> _todaysAppointments = [];
  List<Appointment> _upcomingAppointments = [];
  Appointment? _selectedAppointment;
  bool _isLoading = false;
  String? _error;
  int _currentPage = 1;
  bool _hasMoreData = true;

  // Getters
  List<Appointment> get appointments => _appointments;
  List<Appointment> get todaysAppointments => _todaysAppointments;
  List<Appointment> get upcomingAppointments => _upcomingAppointments;
  Appointment? get selectedAppointment => _selectedAppointment;
  bool get isLoading => _isLoading;
  String? get error => _error;
  int get currentPage => _currentPage;
  bool get hasMoreData => _hasMoreData;

  // Load appointments with pagination and filters
  Future<void> loadAppointments({
    bool refresh = false,
    String? status,
    int? doctorId,
    int? patientId,
    String? dateFrom,
    String? dateTo,
    String? appointmentType,
  }) async {
    if (refresh) {
      _currentPage = 1;
      _appointments.clear();
      _hasMoreData = true;
    }

    if (_isLoading || !_hasMoreData) return;

    _setLoading(true);
    _setError(null);

    try {
      final response = await _apiService.getAppointments(
        page: _currentPage,
        limit: 20,
        status: status,
        doctorId: doctorId,
        patientId: patientId,
        dateFrom: dateFrom,
        dateTo: dateTo,
        appointmentType: appointmentType,
      );

      if (response['success'] == true) {
        final List<dynamic> appointmentData = response['data'] as List<dynamic>;
        final List<Appointment> newAppointments = appointmentData
            .map((json) => Appointment.fromJson(json as Map<String, dynamic>))
            .toList();

        if (refresh) {
          _appointments = newAppointments;
        } else {
          _appointments.addAll(newAppointments);
        }

        _currentPage++;
        _hasMoreData = newAppointments.length == 20;
      } else {
        _setError(response['error'] ?? 'Failed to load appointments');
      }
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  // Load today's appointments
  Future<void> loadTodaysAppointments() async {
    _setLoading(true);
    _setError(null);

    try {
      final response = await _apiService.getTodaysAppointments();

      if (response['success'] == true) {
        final List<dynamic> appointmentData = response['data'] as List<dynamic>;
        _todaysAppointments = appointmentData
            .map((json) => Appointment.fromJson(json as Map<String, dynamic>))
            .toList();
      } else {
        _setError(response['error'] ?? 'Failed to load today\'s appointments');
      }
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  // Load upcoming appointments
  Future<void> loadUpcomingAppointments({int days = 7}) async {
    _setLoading(true);
    _setError(null);

    try {
      final response = await _apiService.getUpcomingAppointments(days: days);

      if (response['success'] == true) {
        final List<dynamic> appointmentData = response['data'] as List<dynamic>;
        _upcomingAppointments = appointmentData
            .map((json) => Appointment.fromJson(json as Map<String, dynamic>))
            .toList();
      } else {
        _setError(response['error'] ?? 'Failed to load upcoming appointments');
      }
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  // Load a specific appointment
  Future<void> loadAppointment(int id) async {
    _setLoading(true);
    _setError(null);

    try {
      final response = await _apiService.getAppointment(id);

      if (response['success'] == true) {
        _selectedAppointment = Appointment.fromJson(response['data'] as Map<String, dynamic>);
      } else {
        _setError(response['error'] ?? 'Failed to load appointment');
      }
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  // Book a new appointment
  Future<bool> bookAppointment(Map<String, dynamic> appointmentData) async {
    _setLoading(true);
    _setError(null);

    try {
      final response = await _apiService.bookAppointment(appointmentData);

      if (response['success'] == true) {
        final newAppointment = Appointment.fromJson(response['data'] as Map<String, dynamic>);
        _appointments.insert(0, newAppointment);
        
        // Update today's appointments if it's for today
        if (newAppointment.isToday) {
          _todaysAppointments.add(newAppointment);
          _todaysAppointments.sort((a, b) => a.appointmentTime.compareTo(b.appointmentTime));
        }

        // Update upcoming appointments if it's in the future
        if (newAppointment.isUpcoming) {
          _upcomingAppointments.add(newAppointment);
          _upcomingAppointments.sort((a, b) => a.appointmentDateTime.compareTo(b.appointmentDateTime));
        }

        notifyListeners();
        return true;
      } else {
        _setError(response['error'] ?? 'Failed to book appointment');
        return false;
      }
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Update an appointment
  Future<bool> updateAppointment(int id, Map<String, dynamic> appointmentData) async {
    _setLoading(true);
    _setError(null);

    try {
      final response = await _apiService.updateAppointment(id, appointmentData);

      if (response['success'] == true) {
        final updatedAppointment = Appointment.fromJson(response['data'] as Map<String, dynamic>);
        
        // Update in the main list
        final index = _appointments.indexWhere((a) => a.id == id);
        if (index != -1) {
          _appointments[index] = updatedAppointment;
        }

        // Update in today's appointments
        final todayIndex = _todaysAppointments.indexWhere((a) => a.id == id);
        if (todayIndex != -1) {
          if (updatedAppointment.isToday) {
            _todaysAppointments[todayIndex] = updatedAppointment;
          } else {
            _todaysAppointments.removeAt(todayIndex);
          }
        }

        // Update in upcoming appointments
        final upcomingIndex = _upcomingAppointments.indexWhere((a) => a.id == id);
        if (upcomingIndex != -1) {
          if (updatedAppointment.isUpcoming) {
            _upcomingAppointments[upcomingIndex] = updatedAppointment;
          } else {
            _upcomingAppointments.removeAt(upcomingIndex);
          }
        }

        // Update selected appointment if it's the same
        if (_selectedAppointment?.id == id) {
          _selectedAppointment = updatedAppointment;
        }

        notifyListeners();
        return true;
      } else {
        _setError(response['error'] ?? 'Failed to update appointment');
        return false;
      }
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Filter appointments by status
  Future<void> filterByStatus(String status) async {
    await loadAppointments(refresh: true, status: status);
  }

  // Filter appointments by doctor
  Future<void> filterByDoctor(int doctorId) async {
    await loadAppointments(refresh: true, doctorId: doctorId);
  }

  // Filter appointments by patient
  Future<void> filterByPatient(int patientId) async {
    await loadAppointments(refresh: true, patientId: patientId);
  }

  // Filter appointments by date range
  Future<void> filterByDateRange(String dateFrom, String dateTo) async {
    await loadAppointments(refresh: true, dateFrom: dateFrom, dateTo: dateTo);
  }

  // Clear selected appointment
  void clearSelectedAppointment() {
    _selectedAppointment = null;
    notifyListeners();
  }

  // Clear error
  void clearError() {
    _setError(null);
  }

  // Private helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }

  // Refresh data
  Future<void> refresh() async {
    await Future.wait([
      loadAppointments(refresh: true),
      loadTodaysAppointments(),
      loadUpcomingAppointments(),
    ]);
  }
}
