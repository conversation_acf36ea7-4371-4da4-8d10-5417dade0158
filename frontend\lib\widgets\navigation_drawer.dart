import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_design_icons_flutter/material_design_icons_flutter.dart';
import '../providers/auth_provider.dart';

class NavigationDrawer extends StatelessWidget {
  const NavigationDrawer({super.key});

  @override
  Widget build(BuildContext context) {
    return Drawer(
      child: Column(
        children: [
          // Header
          Container(
            height: 200.h,
            width: double.infinity,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Theme.of(context).primaryColor,
                  Theme.of(context).primaryColor.withOpacity(0.8),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
            ),
            child: SafeArea(
              child: Padding(
                padding: EdgeInsets.all(20.w),
                child: Consumer<AuthProvider>(
                  builder: (context, authProvider, child) {
                    final user = authProvider.currentUser;
                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        CircleAvatar(
                          radius: 30.r,
                          backgroundColor: Colors.white,
                          child: Text(
                            user?.firstName[0].toUpperCase() ?? 'U',
                            style: TextStyle(
                              fontSize: 24.sp,
                              fontWeight: FontWeight.bold,
                              color: Theme.of(context).primaryColor,
                            ),
                          ),
                        ),
                        SizedBox(height: 12.h),
                        Text(
                          user?.fullName ?? 'User',
                          style: TextStyle(
                            fontSize: 18.sp,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                        Text(
                          user?.role.toUpperCase() ?? 'ROLE',
                          style: TextStyle(
                            fontSize: 14.sp,
                            color: Colors.white.withOpacity(0.9),
                          ),
                        ),
                      ],
                    );
                  },
                ),
              ),
            ),
          ),

          // Menu items
          Expanded(
            child: ListView(
              padding: EdgeInsets.zero,
              children: [
                _buildDrawerItem(
                  context,
                  icon: MdiIcons.viewDashboard,
                  title: 'Dashboard',
                  route: '/',
                ),
                _buildDrawerItem(
                  context,
                  icon: MdiIcons.accountGroup,
                  title: 'Patients',
                  route: '/patients',
                ),
                _buildDrawerItem(
                  context,
                  icon: MdiIcons.doctorIcon,
                  title: 'Doctors',
                  route: '/doctors',
                ),
                _buildDrawerItem(
                  context,
                  icon: MdiIcons.calendar,
                  title: 'Appointments',
                  route: '/appointments',
                ),
                _buildDrawerItem(
                  context,
                  icon: MdiIcons.receipt,
                  title: 'Billing',
                  route: '/billing',
                ),
                _buildDrawerItem(
                  context,
                  icon: MdiIcons.chartLine,
                  title: 'Analytics',
                  route: '/analytics',
                ),
                const Divider(),
                _buildDrawerItem(
                  context,
                  icon: MdiIcons.cog,
                  title: 'Settings',
                  route: '/settings',
                ),
                _buildDrawerItem(
                  context,
                  icon: MdiIcons.help,
                  title: 'Help',
                  route: '/help',
                ),
                ListTile(
                  leading: Icon(
                    MdiIcons.logout,
                    color: Colors.red[600],
                  ),
                  title: Text(
                    'Logout',
                    style: TextStyle(
                      color: Colors.red[600],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  onTap: () async {
                    Navigator.of(context).pop();
                    final authProvider = Provider.of<AuthProvider>(context, listen: false);
                    await authProvider.logout();
                  },
                ),
              ],
            ),
          ),

          // Footer
          Container(
            padding: EdgeInsets.all(16.w),
            child: Text(
              'Version 1.0.0',
              style: TextStyle(
                fontSize: 12.sp,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDrawerItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String route,
  }) {
    final currentRoute = GoRouterState.of(context).uri.path;
    final isSelected = currentRoute == route;

    return ListTile(
      leading: Icon(
        icon,
        color: isSelected ? Theme.of(context).primaryColor : Colors.grey[600],
      ),
      title: Text(
        title,
        style: TextStyle(
          color: isSelected ? Theme.of(context).primaryColor : Colors.grey[800],
          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
        ),
      ),
      selected: isSelected,
      selectedTileColor: Theme.of(context).primaryColor.withOpacity(0.1),
      onTap: () {
        Navigator.of(context).pop();
        if (!isSelected) {
          context.go(route);
        }
      },
    );
  }
}
