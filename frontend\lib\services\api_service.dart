import 'dart:convert';
import 'package:http/http.dart' as http;

class ApiService {
  static const String baseUrl = 'http://localhost:3000/api';
  
  final http.Client _client = http.Client();

  // Helper method to handle HTTP requests
  Future<Map<String, dynamic>> _makeRequest(
    String method,
    String endpoint, {
    Map<String, dynamic>? body,
    Map<String, String>? queryParams,
  }) async {
    try {
      final uri = Uri.parse('$baseUrl$endpoint').replace(
        queryParameters: queryParams,
      );

      late http.Response response;
      final headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      };

      switch (method.toUpperCase()) {
        case 'GET':
          response = await _client.get(uri, headers: headers);
          break;
        case 'POST':
          response = await _client.post(
            uri,
            headers: headers,
            body: body != null ? jsonEncode(body) : null,
          );
          break;
        case 'PUT':
          response = await _client.put(
            uri,
            headers: headers,
            body: body != null ? jsonEncode(body) : null,
          );
          break;
        case 'DELETE':
          response = await _client.delete(uri, headers: headers);
          break;
        default:
          throw Exception('Unsupported HTTP method: $method');
      }

      final responseData = jsonDecode(response.body) as Map<String, dynamic>;

      if (response.statusCode >= 200 && response.statusCode < 300) {
        return responseData;
      } else {
        throw ApiException(
          message: responseData['error'] ?? 'Unknown error occurred',
          statusCode: response.statusCode,
          details: responseData['details'],
        );
      }
    } catch (e) {
      if (e is ApiException) rethrow;
      throw ApiException(
        message: 'Network error: ${e.toString()}',
        statusCode: 0,
      );
    }
  }

  // Patient endpoints
  Future<Map<String, dynamic>> getPatients({
    int page = 1,
    int limit = 20,
    String? status,
    String? search,
    String? bloodType,
  }) async {
    final queryParams = <String, String>{
      'page': page.toString(),
      'limit': limit.toString(),
    };
    
    if (status != null) queryParams['status'] = status;
    if (search != null) queryParams['search'] = search;
    if (bloodType != null) queryParams['blood_type'] = bloodType;

    return _makeRequest('GET', '/patients', queryParams: queryParams);
  }

  Future<Map<String, dynamic>> getPatient(int id) async {
    return _makeRequest('GET', '/patients/$id');
  }

  Future<Map<String, dynamic>> createPatient(Map<String, dynamic> patientData) async {
    return _makeRequest('POST', '/patients', body: patientData);
  }

  Future<Map<String, dynamic>> updatePatient(int id, Map<String, dynamic> patientData) async {
    return _makeRequest('PUT', '/patients/$id', body: patientData);
  }

  Future<Map<String, dynamic>> deletePatient(int id) async {
    return _makeRequest('DELETE', '/patients/$id');
  }

  Future<Map<String, dynamic>> getPatientStatistics() async {
    return _makeRequest('GET', '/patients/statistics');
  }

  // Doctor endpoints
  Future<Map<String, dynamic>> getDoctors({
    int page = 1,
    int limit = 20,
    String? status,
    String? specialization,
    int? departmentId,
    String? search,
  }) async {
    final queryParams = <String, String>{
      'page': page.toString(),
      'limit': limit.toString(),
    };
    
    if (status != null) queryParams['status'] = status;
    if (specialization != null) queryParams['specialization'] = specialization;
    if (departmentId != null) queryParams['department_id'] = departmentId.toString();
    if (search != null) queryParams['search'] = search;

    return _makeRequest('GET', '/doctors', queryParams: queryParams);
  }

  Future<Map<String, dynamic>> getDoctor(int id) async {
    return _makeRequest('GET', '/doctors/$id');
  }

  Future<Map<String, dynamic>> getDoctorStatistics() async {
    return _makeRequest('GET', '/doctors/statistics');
  }

  Future<Map<String, dynamic>> getAvailableDoctors(String date, String time) async {
    return _makeRequest('GET', '/doctors/available', queryParams: {
      'date': date,
      'time': time,
    });
  }

  // Appointment endpoints
  Future<Map<String, dynamic>> getAppointments({
    int page = 1,
    int limit = 20,
    String? status,
    int? doctorId,
    int? patientId,
    String? dateFrom,
    String? dateTo,
    String? appointmentType,
  }) async {
    final queryParams = <String, String>{
      'page': page.toString(),
      'limit': limit.toString(),
    };
    
    if (status != null) queryParams['status'] = status;
    if (doctorId != null) queryParams['doctor_id'] = doctorId.toString();
    if (patientId != null) queryParams['patient_id'] = patientId.toString();
    if (dateFrom != null) queryParams['date_from'] = dateFrom;
    if (dateTo != null) queryParams['date_to'] = dateTo;
    if (appointmentType != null) queryParams['appointment_type'] = appointmentType;

    return _makeRequest('GET', '/appointments', queryParams: queryParams);
  }

  Future<Map<String, dynamic>> getAppointment(int id) async {
    return _makeRequest('GET', '/appointments/$id');
  }

  Future<Map<String, dynamic>> bookAppointment(Map<String, dynamic> appointmentData) async {
    return _makeRequest('POST', '/appointments/book', body: appointmentData);
  }

  Future<Map<String, dynamic>> updateAppointment(int id, Map<String, dynamic> appointmentData) async {
    return _makeRequest('PUT', '/appointments/$id', body: appointmentData);
  }

  Future<Map<String, dynamic>> getTodaysAppointments() async {
    return _makeRequest('GET', '/appointments/today');
  }

  Future<Map<String, dynamic>> getUpcomingAppointments({int days = 7}) async {
    return _makeRequest('GET', '/appointments/upcoming', queryParams: {
      'days': days.toString(),
    });
  }

  Future<Map<String, dynamic>> getAppointmentStatistics() async {
    return _makeRequest('GET', '/appointments/statistics');
  }

  // Billing endpoints
  Future<Map<String, dynamic>> getBilling({
    int page = 1,
    int limit = 20,
    String? paymentStatus,
    int? patientId,
    String? dateFrom,
    String? dateTo,
    bool? overdue,
  }) async {
    final queryParams = <String, String>{
      'page': page.toString(),
      'limit': limit.toString(),
    };
    
    if (paymentStatus != null) queryParams['payment_status'] = paymentStatus;
    if (patientId != null) queryParams['patient_id'] = patientId.toString();
    if (dateFrom != null) queryParams['date_from'] = dateFrom;
    if (dateTo != null) queryParams['date_to'] = dateTo;
    if (overdue != null) queryParams['overdue'] = overdue.toString();

    return _makeRequest('GET', '/billing', queryParams: queryParams);
  }

  Future<Map<String, dynamic>> getBillingRecord(int id) async {
    return _makeRequest('GET', '/billing/$id');
  }

  Future<Map<String, dynamic>> processPayment(int id, Map<String, dynamic> paymentData) async {
    return _makeRequest('POST', '/billing/$id/payment', body: paymentData);
  }

  Future<Map<String, dynamic>> getBillingStatistics() async {
    return _makeRequest('GET', '/billing/statistics');
  }

  Future<Map<String, dynamic>> getOverdueBills() async {
    return _makeRequest('GET', '/billing/overdue');
  }

  // Analytics endpoints
  Future<Map<String, dynamic>> getDashboardAnalytics() async {
    return _makeRequest('GET', '/analytics/dashboard');
  }

  Future<Map<String, dynamic>> getPatientDemographics() async {
    return _makeRequest('GET', '/analytics/patient-demographics');
  }

  Future<Map<String, dynamic>> getDoctorPerformance() async {
    return _makeRequest('GET', '/analytics/doctor-performance');
  }

  Future<Map<String, dynamic>> getFinancialSummary({
    String? startDate,
    String? endDate,
  }) async {
    final queryParams = <String, String>{};
    if (startDate != null) queryParams['start_date'] = startDate;
    if (endDate != null) queryParams['end_date'] = endDate;

    return _makeRequest('GET', '/analytics/financial-summary', queryParams: queryParams);
  }

  Future<Map<String, dynamic>> getAppointmentTrends() async {
    return _makeRequest('GET', '/analytics/appointment-trends');
  }

  // Department endpoints
  Future<Map<String, dynamic>> getDepartments({
    int page = 1,
    int limit = 20,
    String? status,
    String? search,
  }) async {
    final queryParams = <String, String>{
      'page': page.toString(),
      'limit': limit.toString(),
    };
    
    if (status != null) queryParams['status'] = status;
    if (search != null) queryParams['search'] = search;

    return _makeRequest('GET', '/departments', queryParams: queryParams);
  }

  Future<Map<String, dynamic>> getDepartmentStatistics() async {
    return _makeRequest('GET', '/departments/statistics');
  }

  void dispose() {
    _client.close();
  }
}

class ApiException implements Exception {
  final String message;
  final int statusCode;
  final dynamic details;

  ApiException({
    required this.message,
    required this.statusCode,
    this.details,
  });

  @override
  String toString() {
    return 'ApiException: $message (Status: $statusCode)';
  }
}
