import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'services/api_service.dart';
import 'services/auth_service.dart';
import 'providers/auth_provider.dart';
import 'providers/patient_provider.dart';
import 'providers/doctor_provider.dart';
import 'providers/appointment_provider.dart';
import 'providers/billing_provider.dart';
import 'providers/analytics_provider.dart';
import 'widgets/auth_wrapper.dart';

void main() {
  runApp(const HospitalManagementApp());
}

class HospitalManagementApp extends StatelessWidget {
  const HospitalManagementApp({super.key});

  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
      designSize: const Size(375, 812),
      minTextAdapt: true,
      splitScreenMode: true,
      builder: (context, child) {
        return MultiProvider(
          providers: [
            Provider<ApiService>(
              create: (_) => ApiService(),
            ),
            Provider<AuthService>(
              create: (_) => AuthService(),
            ),
            ChangeNotifierProvider<AuthProvider>(
              create: (context) => AuthProvider(
                Provider.of<AuthService>(context, listen: false),
              ),
            ),
            ChangeNotifierProxyProvider<ApiService, PatientProvider>(
              create: (context) => PatientProvider(
                Provider.of<ApiService>(context, listen: false),
              ),
              update: (context, apiService, previous) =>
                  previous ?? PatientProvider(apiService),
            ),
            ChangeNotifierProxyProvider<ApiService, DoctorProvider>(
              create: (context) => DoctorProvider(
                Provider.of<ApiService>(context, listen: false),
              ),
              update: (context, apiService, previous) =>
                  previous ?? DoctorProvider(apiService),
            ),
            ChangeNotifierProxyProvider<ApiService, AppointmentProvider>(
              create: (context) => AppointmentProvider(
                Provider.of<ApiService>(context, listen: false),
              ),
              update: (context, apiService, previous) =>
                  previous ?? AppointmentProvider(apiService),
            ),
            ChangeNotifierProxyProvider<ApiService, BillingProvider>(
              create: (context) => BillingProvider(
                Provider.of<ApiService>(context, listen: false),
              ),
              update: (context, apiService, previous) =>
                  previous ?? BillingProvider(apiService),
            ),
            ChangeNotifierProxyProvider<ApiService, AnalyticsProvider>(
              create: (context) => AnalyticsProvider(
                Provider.of<ApiService>(context, listen: false),
              ),
              update: (context, apiService, previous) =>
                  previous ?? AnalyticsProvider(apiService),
            ),
          ],
          child: MaterialApp(
            title: 'Hospital Management System',
            debugShowCheckedModeBanner: false,
            theme: ThemeData(
              primarySwatch: Colors.blue,
              primaryColor: const Color(0xFF2196F3),
              colorScheme: ColorScheme.fromSeed(
                seedColor: const Color(0xFF2196F3),
                brightness: Brightness.light,
              ),
              useMaterial3: true,
              appBarTheme: const AppBarTheme(
                backgroundColor: Color(0xFF2196F3),
                foregroundColor: Colors.white,
                elevation: 2,
              ),
              cardTheme: CardTheme(
                elevation: 4,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              elevatedButtonTheme: ElevatedButtonThemeData(
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF2196F3),
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ),
            home: const AuthWrapper(),
          ),
        );
      },
    );
  }
}


