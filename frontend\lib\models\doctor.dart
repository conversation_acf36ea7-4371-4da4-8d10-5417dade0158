class Doctor {
  final int id;
  final String firstName;
  final String lastName;
  final String? email;
  final String? phone;
  final String licenseNumber;
  final String specialization;
  final int? departmentId;
  final DateTime hireDate;
  final double? salary;
  final int? experienceYears;
  final String? qualification;
  final String? address;
  final DateTime? dateOfBirth;
  final String? gender;
  final String status;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String? departmentName;
  final int? appointmentCount;
  final double? completionRate;
  final int? totalAppointments;
  final int? completedAppointments;
  final int? recentAppointments;

  Doctor({
    required this.id,
    required this.firstName,
    required this.lastName,
    this.email,
    this.phone,
    required this.licenseNumber,
    required this.specialization,
    this.departmentId,
    required this.hireDate,
    this.salary,
    this.experienceYears,
    this.qualification,
    this.address,
    this.dateOfBirth,
    this.gender,
    required this.status,
    required this.createdAt,
    required this.updatedAt,
    this.departmentName,
    this.appointmentCount,
    this.completionRate,
    this.totalAppointments,
    this.completedAppointments,
    this.recentAppointments,
  });

  String get fullName => '$firstName $lastName';

  int get age {
    if (dateOfBirth == null) return 0;
    final now = DateTime.now();
    int age = now.year - dateOfBirth!.year;
    if (now.month < dateOfBirth!.month ||
        (now.month == dateOfBirth!.month && now.day < dateOfBirth!.day)) {
      age--;
    }
    return age;
  }

  factory Doctor.fromJson(Map<String, dynamic> json) {
    return Doctor(
      id: json['id'] as int,
      firstName: json['first_name'] as String,
      lastName: json['last_name'] as String,
      email: json['email'] as String?,
      phone: json['phone'] as String?,
      licenseNumber: json['license_number'] as String,
      specialization: json['specialization'] as String,
      departmentId: json['department_id'] as int?,
      hireDate: DateTime.parse(json['hire_date'] as String),
      salary: json['salary']?.toDouble(),
      experienceYears: json['experience_years'] as int?,
      qualification: json['qualification'] as String?,
      address: json['address'] as String?,
      dateOfBirth: json['date_of_birth'] != null
          ? DateTime.parse(json['date_of_birth'] as String)
          : null,
      gender: json['gender'] as String?,
      status: json['status'] as String,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      departmentName: json['department_name'] as String?,
      appointmentCount: json['appointment_count'] as int?,
      completionRate: json['completion_rate']?.toDouble(),
      totalAppointments: json['total_appointments'] as int?,
      completedAppointments: json['completed_appointments'] as int?,
      recentAppointments: json['recent_appointments'] as int?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'first_name': firstName,
      'last_name': lastName,
      'email': email,
      'phone': phone,
      'license_number': licenseNumber,
      'specialization': specialization,
      'department_id': departmentId,
      'hire_date': hireDate.toIso8601String().split('T')[0],
      'salary': salary,
      'experience_years': experienceYears,
      'qualification': qualification,
      'address': address,
      'date_of_birth': dateOfBirth?.toIso8601String().split('T')[0],
      'gender': gender,
      'status': status,
    };
  }

  Doctor copyWith({
    int? id,
    String? firstName,
    String? lastName,
    String? email,
    String? phone,
    String? licenseNumber,
    String? specialization,
    int? departmentId,
    DateTime? hireDate,
    double? salary,
    int? experienceYears,
    String? qualification,
    String? address,
    DateTime? dateOfBirth,
    String? gender,
    String? status,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? departmentName,
    int? appointmentCount,
    double? completionRate,
    int? totalAppointments,
    int? completedAppointments,
    int? recentAppointments,
  }) {
    return Doctor(
      id: id ?? this.id,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      licenseNumber: licenseNumber ?? this.licenseNumber,
      specialization: specialization ?? this.specialization,
      departmentId: departmentId ?? this.departmentId,
      hireDate: hireDate ?? this.hireDate,
      salary: salary ?? this.salary,
      experienceYears: experienceYears ?? this.experienceYears,
      qualification: qualification ?? this.qualification,
      address: address ?? this.address,
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      gender: gender ?? this.gender,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      departmentName: departmentName ?? this.departmentName,
      appointmentCount: appointmentCount ?? this.appointmentCount,
      completionRate: completionRate ?? this.completionRate,
      totalAppointments: totalAppointments ?? this.totalAppointments,
      completedAppointments: completedAppointments ?? this.completedAppointments,
      recentAppointments: recentAppointments ?? this.recentAppointments,
    );
  }
}
